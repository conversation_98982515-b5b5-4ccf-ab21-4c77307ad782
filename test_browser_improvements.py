#!/usr/bin/env python3
"""
Script de prueba para verificar las mejoras del browser helper.
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append('src')

from src.Utilities.browser_helper import (
    create_and_run_agent,
    BrowserHelperConfig,
    validate_config
)
from src.Config.browser_config import (
    BrowserConfigurations,
    get_config_by_type,
    EnvironmentBasedConfig
)
from src.Utilities.utils import controller


async def test_backward_compatibility():
    """Test 1: Verificar compatibilidad hacia atrás"""
    print("🔄 Test 1: Compatibilidad hacia atrás")
    
    scenario = """
    Given I am on https://httpbin.org/
    When I look for the status codes section
    Then I should see HTTP status information
    """
    
    try:
        # Usar la función exactamente como antes
        history = await create_and_run_agent(
            scenario_text=scenario,
            controller_instance=controller,
            language="en"
        )
        
        print(f"✅ Compatibilidad OK - {len(history.action_names())} acciones ejecutadas")
        return True
        
    except Exception as e:
        print(f"❌ Error en compatibilidad: {e}")
        return False


async def test_predefined_configurations():
    """Test 2: Verificar configuraciones predefinidas"""
    print("\n🔧 Test 2: Configuraciones predefinidas")
    
    scenario = """
    Given I am on https://httpbin.org/html
    When I examine the page content
    Then I should see HTML content
    """
    
    configs_to_test = [
        ("CI/CD", get_config_by_type("ci")),
        ("Smoke", get_config_by_type("smoke")),
        ("Web", get_config_by_type("web"))
    ]
    
    results = []
    
    for config_name, config in configs_to_test:
        try:
            print(f"  🧪 Probando configuración {config_name}...")
            
            # Validar configuración
            warnings = validate_config(config)
            if warnings:
                print(f"    ⚠️ Warnings: {warnings}")
            
            history = await create_and_run_agent(
                scenario_text=scenario,
                controller_instance=controller,
                config=config,
                language="en"
            )
            
            print(f"    ✅ {config_name} OK - {len(history.action_names())} acciones")
            results.append(True)
            
        except Exception as e:
            print(f"    ❌ {config_name} falló: {e}")
            results.append(False)
    
    return all(results)


async def test_custom_configuration():
    """Test 3: Verificar configuración personalizada"""
    print("\n⚙️ Test 3: Configuración personalizada")
    
    scenario = """
    Given I am on https://httpbin.org/
    When I navigate to the forms section
    Then I should see form examples
    """
    
    try:
        # Crear configuración personalizada
        custom_config = BrowserHelperConfig(
            headless=True,
            use_vision=True,
            enable_memory=False,  # Desactivar para test rápido
            max_steps=20,
            minimum_wait_page_load_time=0.3,
            wait_for_network_idle_page_load_time=0.5,
            maximum_wait_page_load_time=8.0,
            wait_between_actions=0.3,
            model_provider="gemini"
        )
        
        # Validar configuración
        warnings = validate_config(custom_config)
        if warnings:
            print(f"  ⚠️ Warnings: {warnings}")
        
        history = await create_and_run_agent(
            scenario_text=scenario,
            controller_instance=controller,
            config=custom_config,
            language="en"
        )
        
        print(f"✅ Configuración personalizada OK - {len(history.action_names())} acciones")
        return True
        
    except Exception as e:
        print(f"❌ Error en configuración personalizada: {e}")
        return False


async def test_environment_based_config():
    """Test 4: Verificar configuración basada en entorno"""
    print("\n🌍 Test 4: Configuración basada en entorno")
    
    scenario = """
    Given I am on https://httpbin.org/status/200
    When I check the response
    Then I should see a 200 status
    """
    
    try:
        # Obtener configuración automática basada en entorno
        env_config = EnvironmentBasedConfig.get_config_for_environment()
        
        # Validar configuración
        warnings = validate_config(env_config)
        if warnings:
            print(f"  ⚠️ Warnings: {warnings}")
        
        history = await create_and_run_agent(
            scenario_text=scenario,
            controller_instance=controller,
            config=env_config,
            language="en"
        )
        
        print(f"✅ Configuración de entorno OK - {len(history.action_names())} acciones")
        return True
        
    except Exception as e:
        print(f"❌ Error en configuración de entorno: {e}")
        return False


def test_configuration_validation():
    """Test 5: Verificar validación de configuraciones"""
    print("\n✅ Test 5: Validación de configuraciones")
    
    try:
        # Configuración válida
        valid_config = BrowserHelperConfig()
        warnings = validate_config(valid_config)
        print(f"  📋 Configuración válida - warnings: {len(warnings)}")
        
        # Configuración con problemas potenciales
        problematic_config = BrowserHelperConfig(
            headless=False,  # Puede causar warning si no hay display
            viewport_expansion=-5,  # Valor inválido
            enable_memory=True,
            max_steps=5  # Muy pocos pasos para memoria
        )
        
        warnings = validate_config(problematic_config)
        print(f"  ⚠️ Configuración problemática - warnings: {len(warnings)}")
        for warning in warnings:
            print(f"    - {warning}")
        
        print("✅ Validación funcionando correctamente")
        return True
        
    except Exception as e:
        print(f"❌ Error en validación: {e}")
        return False


async def test_different_model_providers():
    """Test 6: Verificar diferentes proveedores de modelo"""
    print("\n🤖 Test 6: Proveedores de modelo")
    
    scenario = """
    Given I am on https://httpbin.org/json
    When I examine the JSON response
    Then I should see JSON data
    """
    
    # Detectar proveedores disponibles
    available_providers = []
    
    if os.getenv("GOOGLE_API_KEY"):
        available_providers.append(("gemini", "gemini-2.0-flash"))
    
    if os.getenv("OPENAI_API_KEY"):
        available_providers.append(("openai", "gpt-4o"))
    
    if os.getenv("ANTHROPIC_API_KEY"):
        available_providers.append(("anthropic", "claude-3-5-sonnet-20240620"))
    
    if not available_providers:
        print("  ⚠️ No hay API keys disponibles para probar proveedores")
        return True
    
    results = []
    
    for provider, model in available_providers:
        try:
            print(f"  🧪 Probando {provider} ({model})...")
            
            config = BrowserHelperConfig(
                model_provider=provider,
                model_name=model,
                headless=True,
                use_vision=True,
                enable_memory=False,
                max_steps=15
            )
            
            history = await create_and_run_agent(
                scenario_text=scenario,
                controller_instance=controller,
                config=config,
                language="en"
            )
            
            print(f"    ✅ {provider} OK - {len(history.action_names())} acciones")
            results.append(True)
            
        except Exception as e:
            print(f"    ❌ {provider} falló: {e}")
            results.append(False)
    
    return all(results)


async def main():
    """Ejecutar todos los tests"""
    print("🚀 Iniciando tests de mejoras del browser helper")
    print("=" * 60)
    
    # Verificar API key básica
    if not os.getenv("GOOGLE_API_KEY"):
        print("❌ GOOGLE_API_KEY no encontrada")
        print("Por favor configura tu API key de Google para ejecutar los tests")
        return
    
    # Ejecutar tests
    tests = [
        ("Compatibilidad hacia atrás", test_backward_compatibility()),
        ("Configuraciones predefinidas", test_predefined_configurations()),
        ("Configuración personalizada", test_custom_configuration()),
        ("Configuración de entorno", test_environment_based_config()),
        ("Validación de configuraciones", test_configuration_validation()),
        ("Proveedores de modelo", test_different_model_providers())
    ]
    
    results = []
    
    for test_name, test_coro in tests:
        try:
            if asyncio.iscoroutine(test_coro):
                result = await test_coro
            else:
                result = test_coro
            results.append(result)
        except Exception as e:
            print(f"❌ Error en test '{test_name}': {e}")
            results.append(False)
        
        # Pequeña pausa entre tests
        await asyncio.sleep(1)
    
    # Resumen
    print("\n" + "=" * 60)
    print("📊 RESUMEN DE TESTS")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ PASS" if results[i] else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Resultado: {passed}/{total} tests pasaron")
    
    if passed == total:
        print("🎉 ¡Todas las mejoras funcionan correctamente!")
        print("\n📋 Próximos pasos:")
        print("1. Revisar la documentación en docs/migration_guide.md")
        print("2. Experimentar con diferentes configuraciones")
        print("3. Aplicar configuraciones específicas según tus necesidades")
    else:
        print("⚠️ Algunos tests fallaron. Revisar los errores arriba.")
        print("💡 Tip: Verificar variables de entorno y conectividad")


if __name__ == "__main__":
    asyncio.run(main())
