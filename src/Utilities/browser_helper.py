import os
from typing import Op<PERSON>
from browser_use import Browser, Agent as BrowserAgent
from langchain_google_genai import ChatGoogleGenerativeAI
#generate_browser_task está en src.Prompts.browser_prompts
from src.Prompts.browser_prompts import generate_browser_task


async def create_and_run_agent(
    scenario_text: str,
    controller_instance,
    api_key: str = None,
    language: Optional[str] = None,  # Added language parameter
    token_optimized: bool = False,  # Mantenemos el parámetro para compatibilidad pero lo ignoramos
    config = None  # Nuevo parámetro para configuración avanzada
):
    """
    Crea, configura y ejecuta un BrowserAgent para un escenario dado.
    Devuelve el historial de la ejecución.
    """
    if api_key is None:
        api_key = os.environ.get("GOOGLE_API_KEY")

    if not api_key:
        raise ValueError("API key for Google Generative AI not found. Please set the GOOGLE_API_KEY environment variable or pass an api_key.")

    # Use provided language, or fallback to environment variable, then to 'en'
    effective_language = language if language is not None else os.getenv("PROMPT_LANGUAGE", "en")

    agent_task = generate_browser_task(scenario_text, effective_language)

    # Use Gemini model
    model_name = os.getenv("LLM_MODEL", "gemini-2.0-flash")
    llm_instance = ChatGoogleGenerativeAI(
        model=model_name,
        api_key=api_key
    )

    # Configure browser agent with basic settings
    browser_agent_config = {
        "task": agent_task,
        "llm": llm_instance,
        "controller": controller_instance,
        "use_vision": True  # Mantener visión habilitada para mejor funcionamiento
    }

    # Create the agent
    browser_agent_instance = BrowserAgent(**browser_agent_config)

    history = await browser_agent_instance.run()
    return history


# Configuración simple para compatibilidad
class BrowserHelperConfig:
    def __init__(self, **kwargs):
        # Valores por defecto
        self.headless = kwargs.get('headless', None)
        self.use_vision = kwargs.get('use_vision', True)
        self.max_steps = kwargs.get('max_steps', 100)
        self.model_provider = kwargs.get('model_provider', 'gemini')
        self.model_name = kwargs.get('model_name', None)
        self.temperature = kwargs.get('temperature', 0.0)

        # Almacenar cualquier otro parámetro
        for key, value in kwargs.items():
            if not hasattr(self, key):
                setattr(self, key, value)


# Funciones de compatibilidad simples
def create_fast_config(**kwargs):
    """Configuración rápida para CI/CD"""
    defaults = {
        "headless": True,
        "use_vision": True,  # Mantener visión para mejor funcionamiento
        "max_steps": 30,
        "model_provider": "gemini"
    }
    defaults.update(kwargs)
    return BrowserHelperConfig(**defaults)


def create_robust_config(**kwargs):
    """Configuración robusta para tests completos"""
    defaults = {
        "headless": True,
        "use_vision": True,
        "max_steps": 150,
        "model_provider": "gemini"
    }
    defaults.update(kwargs)
    return BrowserHelperConfig(**defaults)


def create_secure_config(allowed_domains=None, **kwargs):
    """Configuración segura para producción"""
    defaults = {
        "headless": True,
        "use_vision": True,
        "max_steps": 100,
        "model_provider": "gemini"
    }
    if allowed_domains:
        defaults["allowed_domains"] = allowed_domains
    defaults.update(kwargs)
    return BrowserHelperConfig(**defaults)


def create_debug_config(**kwargs):
    """Configuración para debugging"""
    defaults = {
        "headless": False,
        "use_vision": True,
        "max_steps": 50,
        "model_provider": "gemini"
    }
    defaults.update(kwargs)
    return BrowserHelperConfig(**defaults)


def validate_config(config):
    """Validación básica de configuración"""
    warnings = []

    if hasattr(config, 'model_provider') and config.model_provider not in ["gemini", "openai", "anthropic"]:
        warnings.append(f"Unsupported model provider: {config.model_provider}")

    if hasattr(config, 'headless') and not config.headless and os.getenv("DISPLAY") is None:
        warnings.append("Non-headless mode requested but no display available")

    return warnings
