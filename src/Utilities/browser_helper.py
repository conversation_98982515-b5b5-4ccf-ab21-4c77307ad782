import os
import logging
from typing import Optional, Dict, Any, List, Union
from pathlib import Path
from datetime import datetime

from browser_use import B<PERSON><PERSON>, Agent as BrowserAgent, <PERSON>rowserSession, BrowserProfile, MemoryConfig
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import Chat<PERSON>penA<PERSON>
from langchain_anthropic import ChatAnthropic

# Import browser prompts
from src.Prompts.browser_prompts import generate_browser_task

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BrowserHelperConfig:
    """Configuration class for browser helper settings."""

    def __init__(
        self,
        # Browser settings
        headless: Optional[bool] = None,
        user_data_dir: Optional[str] = None,
        allowed_domains: Optional[List[str]] = None,
        disable_security: bool = False,
        deterministic_rendering: bool = True,
        highlight_elements: bool = True,
        viewport_expansion: int = 500,

        # Performance settings
        minimum_wait_page_load_time: float = 0.5,
        wait_for_network_idle_page_load_time: float = 1.0,
        maximum_wait_page_load_time: float = 10.0,
        wait_between_actions: float = 0.5,

        # Agent settings
        max_steps: int = 100,
        use_vision: bool = True,
        save_conversation_path: Optional[str] = None,
        extend_system_message: Optional[str] = None,

        # Memory settings
        enable_memory: bool = True,
        memory_interval: int = 10,

        # Retry and error handling
        max_failures: int = 3,
        retry_delay: int = 10,

        # Advanced features
        generate_gif: Union[bool, str] = False,
        keep_alive: bool = False,

        # Model settings
        model_provider: str = "gemini",  # gemini, openai, anthropic
        model_name: Optional[str] = None,
        temperature: float = 0.0,
        timeout: int = 100
    ):
        self.headless = headless
        self.user_data_dir = user_data_dir
        self.allowed_domains = allowed_domains
        self.disable_security = disable_security
        self.deterministic_rendering = deterministic_rendering
        self.highlight_elements = highlight_elements
        self.viewport_expansion = viewport_expansion

        self.minimum_wait_page_load_time = minimum_wait_page_load_time
        self.wait_for_network_idle_page_load_time = wait_for_network_idle_page_load_time
        self.maximum_wait_page_load_time = maximum_wait_page_load_time
        self.wait_between_actions = wait_between_actions

        self.max_steps = max_steps
        self.use_vision = use_vision
        self.save_conversation_path = save_conversation_path
        self.extend_system_message = extend_system_message

        self.enable_memory = enable_memory
        self.memory_interval = memory_interval

        self.max_failures = max_failures
        self.retry_delay = retry_delay

        self.generate_gif = generate_gif
        self.keep_alive = keep_alive

        self.model_provider = model_provider
        self.model_name = model_name
        self.temperature = temperature
        self.timeout = timeout


def create_llm_instance(config: BrowserHelperConfig, api_key: str):
    """Create LLM instance based on provider configuration."""

    if config.model_provider.lower() == "gemini":
        model_name = config.model_name or os.getenv("LLM_MODEL", "gemini-2.0-flash")
        return ChatGoogleGenerativeAI(
            model=model_name,
            api_key=api_key,
            temperature=config.temperature
        )

    elif config.model_provider.lower() == "openai":
        model_name = config.model_name or "gpt-4o"
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable not found")
        return ChatOpenAI(
            model=model_name,
            api_key=openai_api_key,
            temperature=config.temperature
        )

    elif config.model_provider.lower() == "anthropic":
        model_name = config.model_name or "claude-3-5-sonnet-20240620"
        anthropic_api_key = os.getenv("ANTHROPIC_API_KEY")
        if not anthropic_api_key:
            raise ValueError("ANTHROPIC_API_KEY environment variable not found")
        return ChatAnthropic(
            model_name=model_name,
            api_key=anthropic_api_key,
            temperature=config.temperature,
            timeout=config.timeout
        )

    else:
        raise ValueError(f"Unsupported model provider: {config.model_provider}")


def create_browser_profile(config: BrowserHelperConfig) -> BrowserProfile:
    """Create browser profile with enhanced settings."""

    # Set default user data directory if not provided
    if config.user_data_dir is None:
        config.user_data_dir = "~/.config/browseruse/profiles/agentqa_profile"

    # Auto-detect headless mode if not specified
    if config.headless is None:
        # Try to detect if we're in a headless environment
        config.headless = os.getenv("DISPLAY") is None and os.getenv("WAYLAND_DISPLAY") is None

    return BrowserProfile(
        headless=config.headless,
        user_data_dir=config.user_data_dir,
        allowed_domains=config.allowed_domains,
        disable_security=config.disable_security,
        deterministic_rendering=config.deterministic_rendering,
        highlight_elements=config.highlight_elements,
        viewport_expansion=config.viewport_expansion,
        minimum_wait_page_load_time=config.minimum_wait_page_load_time,
        wait_for_network_idle_page_load_time=config.wait_for_network_idle_page_load_time,
        maximum_wait_page_load_time=config.maximum_wait_page_load_time,
        wait_between_actions=config.wait_between_actions,
        # Additional browser arguments for better automation
        args=[
            "--no-first-run",
            "--disable-blink-features=AutomationControlled",
            "--disable-web-security" if config.disable_security else "",
        ]
    )


def create_memory_config(config: BrowserHelperConfig, llm_provider: str) -> Optional[MemoryConfig]:
    """Create memory configuration based on LLM provider."""

    if not config.enable_memory:
        return None

    # Set embedder based on LLM provider
    embedder_config = {}

    if llm_provider.lower() == "gemini":
        embedder_config = {
            "embedder_provider": "gemini",
            "embedder_model": "models/text-embedding-004"
        }
    elif llm_provider.lower() == "openai":
        embedder_config = {
            "embedder_provider": "openai",
            "embedder_model": "text-embedding-3-small"
        }
    else:
        # Default to HuggingFace for other providers
        embedder_config = {
            "embedder_provider": "huggingface",
            "embedder_model": "all-MiniLM-L6-v2"
        }

    return MemoryConfig(
        agent_id="agentqa_browser_agent",
        memory_interval=config.memory_interval,
        vector_store_provider="faiss",
        vector_store_base_path="/tmp/agentqa_mem0",
        **embedder_config
    )


async def create_and_run_agent(
    scenario_text: str,
    controller_instance,
    api_key: str = None,
    language: Optional[str] = None,
    config: Optional[BrowserHelperConfig] = None,
    # Legacy parameters for backward compatibility
    token_optimized: bool = False  # Ignored - kept for compatibility
) -> Any:
    """
    Enhanced browser agent creation and execution with comprehensive configuration.

    Args:
        scenario_text: The Gherkin scenario to execute
        controller_instance: Browser controller instance
        api_key: API key for the LLM (defaults to environment variable)
        language: Language for prompts ('en' or 'es')
        config: BrowserHelperConfig instance for advanced configuration
        token_optimized: Legacy parameter (ignored)

    Returns:
        Agent execution history

    Raises:
        ValueError: If required API keys are missing
        Exception: If agent execution fails
    """

    # Use default config if none provided
    if config is None:
        config = BrowserHelperConfig()

    # Validate and set API key
    if api_key is None:
        if config.model_provider.lower() == "gemini":
            api_key = os.environ.get("GOOGLE_API_KEY")
            if not api_key:
                raise ValueError("GOOGLE_API_KEY environment variable not found. Please set it or pass an api_key.")
        elif config.model_provider.lower() == "openai":
            api_key = os.environ.get("OPENAI_API_KEY")
            if not api_key:
                raise ValueError("OPENAI_API_KEY environment variable not found.")
        elif config.model_provider.lower() == "anthropic":
            api_key = os.environ.get("ANTHROPIC_API_KEY")
            if not api_key:
                raise ValueError("ANTHROPIC_API_KEY environment variable not found.")

    # Determine effective language
    effective_language = language if language is not None else os.getenv("PROMPT_LANGUAGE", "en")

    # Generate agent task with enhanced prompts
    agent_task = generate_browser_task(scenario_text, effective_language)

    # Add custom system message if provided
    if config.extend_system_message:
        agent_task += f"\n\n{config.extend_system_message}"

    logger.info(f"Creating browser agent with provider: {config.model_provider}")

    try:
        # Create LLM instance
        llm_instance = create_llm_instance(config, api_key)

        # Create browser profile
        browser_profile = create_browser_profile(config)

        # Create browser session
        browser_session = BrowserSession(
            browser_profile=browser_profile,
            keep_alive=config.keep_alive
        )

        # Create memory configuration
        memory_config = create_memory_config(config, config.model_provider)

        # Set up conversation saving if requested
        conversation_path = None
        if config.save_conversation_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            conversation_path = f"{config.save_conversation_path}/conversation_{timestamp}.json"
            os.makedirs(os.path.dirname(conversation_path), exist_ok=True)

        # Configure browser agent with enhanced settings
        agent_kwargs = {
            "task": agent_task,
            "llm": llm_instance,
            "controller": controller_instance,
            "browser_session": browser_session,
            "use_vision": config.use_vision,
            "max_failures": config.max_failures,
            "retry_delay": config.retry_delay,
            "generate_gif": config.generate_gif,
        }

        # Add memory configuration if enabled
        if memory_config:
            agent_kwargs["enable_memory"] = True
            agent_kwargs["memory_config"] = memory_config
        else:
            agent_kwargs["enable_memory"] = False

        # Add conversation saving if configured
        if conversation_path:
            agent_kwargs["save_conversation_path"] = conversation_path

        # Create the agent
        browser_agent_instance = BrowserAgent(**agent_kwargs)

        logger.info("Starting browser agent execution...")

        # Execute the agent with configured max steps
        history = await browser_agent_instance.run(max_steps=config.max_steps)

        logger.info("Browser agent execution completed successfully")

        # Log execution summary
        if hasattr(history, 'action_names'):
            logger.info(f"Executed {len(history.action_names())} actions")
        if hasattr(history, 'urls'):
            logger.info(f"Visited {len(history.urls())} URLs")
        if hasattr(history, 'errors') and history.errors():
            logger.warning(f"Encountered {len(history.errors())} errors during execution")

        return history

    except Exception as e:
        logger.error(f"Browser agent execution failed: {str(e)}")
        raise


# Legacy function for backward compatibility
async def create_and_run_agent_legacy(
    scenario_text: str,
    controller_instance,
    api_key: str = None,
    language: Optional[str] = None,
    token_optimized: bool = False
):
    """
    Legacy function for backward compatibility.
    Uses basic configuration similar to the original implementation.
    """
    basic_config = BrowserHelperConfig(
        model_provider="gemini",
        use_vision=True,
        enable_memory=False,  # Disable memory for legacy compatibility
        headless=None,  # Auto-detect
        max_steps=100
    )

    return await create_and_run_agent(
        scenario_text=scenario_text,
        controller_instance=controller_instance,
        api_key=api_key,
        language=language,
        config=basic_config
    )


# Utility functions for creating common configurations

def create_fast_config(**kwargs) -> BrowserHelperConfig:
    """Create a configuration optimized for speed and minimal resource usage."""
    defaults = {
        "headless": True,
        "use_vision": False,  # Disable vision for speed
        "enable_memory": False,  # Disable memory for speed
        "deterministic_rendering": False,  # Disable for speed
        "highlight_elements": False,  # Disable for speed
        "viewport_expansion": 0,  # Minimal viewport
        "minimum_wait_page_load_time": 0.1,
        "wait_for_network_idle_page_load_time": 0.3,
        "maximum_wait_page_load_time": 5.0,
        "wait_between_actions": 0.1,
        "max_steps": 50,
        "model_provider": "gemini",
        "temperature": 0.0
    }
    defaults.update(kwargs)
    return BrowserHelperConfig(**defaults)


def create_robust_config(**kwargs) -> BrowserHelperConfig:
    """Create a configuration optimized for reliability and comprehensive testing."""
    defaults = {
        "headless": False,  # Visual mode for debugging
        "use_vision": True,
        "enable_memory": True,
        "deterministic_rendering": True,
        "highlight_elements": True,
        "viewport_expansion": 1000,  # Larger viewport for more context
        "minimum_wait_page_load_time": 1.0,
        "wait_for_network_idle_page_load_time": 2.0,
        "maximum_wait_page_load_time": 15.0,
        "wait_between_actions": 1.0,
        "max_steps": 200,
        "max_failures": 5,
        "retry_delay": 15,
        "model_provider": "gemini",
        "temperature": 0.0,
        "generate_gif": True
    }
    defaults.update(kwargs)
    return BrowserHelperConfig(**defaults)


def create_secure_config(allowed_domains: List[str], **kwargs) -> BrowserHelperConfig:
    """Create a configuration with security restrictions for production use."""
    defaults = {
        "headless": True,
        "allowed_domains": allowed_domains,
        "disable_security": False,  # Keep security enabled
        "use_vision": True,
        "enable_memory": True,
        "deterministic_rendering": True,
        "highlight_elements": False,  # Disable for production
        "viewport_expansion": 500,
        "max_steps": 100,
        "max_failures": 3,
        "model_provider": "gemini",
        "temperature": 0.0
    }
    defaults.update(kwargs)
    return BrowserHelperConfig(**defaults)


def create_debug_config(**kwargs) -> BrowserHelperConfig:
    """Create a configuration optimized for debugging and development."""
    defaults = {
        "headless": False,
        "use_vision": True,
        "enable_memory": False,  # Disable for simpler debugging
        "deterministic_rendering": True,
        "highlight_elements": True,
        "viewport_expansion": 1000,
        "minimum_wait_page_load_time": 1.0,
        "wait_for_network_idle_page_load_time": 2.0,
        "maximum_wait_page_load_time": 20.0,
        "wait_between_actions": 2.0,  # Slower for observation
        "max_steps": 50,
        "max_failures": 1,  # Fail fast for debugging
        "model_provider": "gemini",
        "temperature": 0.0,
        "save_conversation_path": "./debug_conversations",
        "generate_gif": True
    }
    defaults.update(kwargs)
    return BrowserHelperConfig(**defaults)


# Configuration validation
def validate_config(config: BrowserHelperConfig) -> List[str]:
    """Validate configuration and return list of warnings/issues."""
    warnings = []

    if config.model_provider.lower() not in ["gemini", "openai", "anthropic"]:
        warnings.append(f"Unsupported model provider: {config.model_provider}")

    if config.use_vision and config.model_provider.lower() == "anthropic":
        # Some Anthropic models may not support vision
        warnings.append("Vision may not be supported with all Anthropic models")

    if config.enable_memory and config.max_steps < 20:
        warnings.append("Memory is enabled but max_steps is low - memory may not be effective")

    if config.allowed_domains and config.disable_security:
        warnings.append("Domain restrictions may not be effective with security disabled")

    if not config.headless and os.getenv("DISPLAY") is None:
        warnings.append("Non-headless mode requested but no display available")

    if config.viewport_expansion < 0 and config.viewport_expansion != -1:
        warnings.append("Invalid viewport_expansion value (use -1 for full page or positive number)")

    return warnings


# Export main functions and classes
__all__ = [
    'BrowserHelperConfig',
    'create_and_run_agent',
    'create_and_run_agent_legacy',
    'create_fast_config',
    'create_robust_config',
    'create_secure_config',
    'create_debug_config',
    'validate_config'
]
