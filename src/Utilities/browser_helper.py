import os
from typing import Op<PERSON>
from browser_use import Browser, Agent as BrowserAgent
from langchain_google_genai import ChatGoogleGenerativeAI
#generate_browser_task está en src.Prompts.browser_prompts
from src.Prompts.browser_prompts import generate_browser_task


async def create_and_run_agent(
    scenario_text: str,
    controller_instance,
    api_key: str = None,
    language: Optional[str] = None,  # Added language parameter
    token_optimized: bool = False  # Mantenemos el parámetro para compatibilidad pero lo ignoramos
):
    """
    Crea, configura y ejecuta un BrowserAgent para un escenario dado.
    Devuelve el historial de la ejecución.
    """
    if api_key is None:
        api_key = os.environ.get("GOOGLE_API_KEY")

    if not api_key:
        raise ValueError("API key for Google Generative AI not found. Please set the GOOGLE_API_KEY environment variable or pass an api_key.")

    # Use provided language, or fallback to environment variable, then to 'en'
    effective_language = language if language is not None else os.getenv("PROMPT_LANGUAGE", "en")

    agent_task = generate_browser_task(scenario_text, effective_language)

    # Use Gemini model
    model_name = os.getenv("LLM_MODEL", "gemini-2.0-flash")
    llm_instance = ChatGoogleGenerativeAI(
        model=model_name,
        api_key=api_key
    )

    # Configure browser agent with basic settings
    browser_agent_config = {
        "task": agent_task,
        "llm": llm_instance,
        "controller": controller_instance,
        "use_vision": True  # Mantener visión habilitada para mejor funcionamiento
    }

    # Create the agent
    browser_agent_instance = BrowserAgent(**browser_agent_config)

    history = await browser_agent_instance.run()
    return history
