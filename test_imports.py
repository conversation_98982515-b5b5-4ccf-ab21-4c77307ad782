#!/usr/bin/env python3
"""
Script simple para verificar que las importaciones funcionan correctamente.
"""

import sys
import os

# Add src to path
sys.path.append('src')

def test_browser_helper_imports():
    """Test browser helper imports"""
    print("🔄 Testing browser helper imports...")
    
    try:
        from src.Utilities.browser_helper import (
            create_and_run_agent,
            BrowserHelperConfig,
            validate_config,
            BROWSER_SESSION_AVAILABLE,
            MEMORY_CONFIG_AVAILABLE
        )
        print("✅ Browser helper imports successful")
        print(f"   - BrowserSession available: {BROWSER_SESSION_AVAILABLE}")
        print(f"   - MemoryConfig available: {MEMORY_CONFIG_AVAILABLE}")
        return True
    except Exception as e:
        print(f"❌ Browser helper import failed: {e}")
        return False

def test_config_imports():
    """Test config imports"""
    print("\n🔄 Testing config imports...")
    
    try:
        from src.Config.browser_config import (
            BrowserConfigurations,
            get_config_by_type
        )
        print("✅ Config imports successful")
        return True
    except Exception as e:
        print(f"❌ Config import failed: {e}")
        return False

def test_basic_configuration():
    """Test basic configuration creation"""
    print("\n🔄 Testing basic configuration...")
    
    try:
        from src.Utilities.browser_helper import BrowserHelperConfig, validate_config
        
        # Create basic config
        config = BrowserHelperConfig()
        print("✅ Basic config created")
        
        # Validate config
        warnings = validate_config(config)
        print(f"✅ Config validation completed - {len(warnings)} warnings")
        
        for warning in warnings:
            print(f"   ⚠️ {warning}")
        
        return True
    except Exception as e:
        print(f"❌ Basic configuration test failed: {e}")
        return False

def test_predefined_configs():
    """Test predefined configurations"""
    print("\n🔄 Testing predefined configurations...")
    
    try:
        from src.Config.browser_config import get_config_by_type
        
        # Test different config types
        config_types = ["ci", "smoke", "web"]
        
        for config_type in config_types:
            config = get_config_by_type(config_type)
            print(f"✅ {config_type} config created")
        
        return True
    except Exception as e:
        print(f"❌ Predefined configs test failed: {e}")
        return False

def test_browser_use_version():
    """Test browser-use version and available features"""
    print("\n🔄 Testing browser-use version and features...")
    
    try:
        import browser_use
        print(f"✅ browser-use version: {getattr(browser_use, '__version__', 'unknown')}")
        
        # Test what's available
        available_features = []
        
        try:
            from browser_use import BrowserSession
            available_features.append("BrowserSession")
        except ImportError:
            pass
        
        try:
            from browser_use import BrowserProfile
            available_features.append("BrowserProfile")
        except ImportError:
            pass
        
        try:
            from browser_use import MemoryConfig
            available_features.append("MemoryConfig")
        except ImportError:
            pass
        
        print(f"✅ Available features: {', '.join(available_features) if available_features else 'Basic features only'}")
        return True
        
    except Exception as e:
        print(f"❌ browser-use version test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Browser Helper Compatibility")
    print("=" * 50)
    
    tests = [
        ("Browser Helper Imports", test_browser_helper_imports),
        ("Config Imports", test_config_imports),
        ("Basic Configuration", test_basic_configuration),
        ("Predefined Configs", test_predefined_configs),
        ("Browser-use Version", test_browser_use_version)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 SUMMARY")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ PASS" if results[i] else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All imports and basic functionality working!")
        print("\n📋 Next steps:")
        print("1. Try running the Streamlit app: streamlit run app.py")
        print("2. Test with a simple scenario")
        print("3. Check the improved functionality")
    else:
        print("⚠️ Some tests failed. Check the errors above.")
        print("💡 The basic functionality should still work even with warnings.")

if __name__ == "__main__":
    main()
